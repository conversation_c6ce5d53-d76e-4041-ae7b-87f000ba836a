import pytest
import json
import math
import numpy as np
import pandas as pd
from unittest.mock import patch, MagicMock
import sys
import os

# Add the cos_ai_service directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'cos_ai_service'))

from vis_data_preprocess import (
    data_preprocess, get_norm_alpha, norm_list, 
    check_single_interval, ckeck_in_valid_interval, 
    is_sparse, check_param
)


class TestDataPreprocess:
    """Comprehensive unit tests for data_preprocess function"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Mock default values
        self.mock_default = {
            "param_list": {
                "param_sparse": 0.80,
                "param_percent": 0.15,
                "param_smooth": 8,
                "param_dtw": 0.18,
                "param_downsample": 360,
                "param_dtw_low": 1.2,
                "param_dtw_high": 2.4,
                "param_sigma": 3,
                "param_cos_sim": 0.35,
                "time_window_focus": 3,
                "time_window_dtw": 240,
                "time_window_nsigma": 45
            },
            "param_valid_interval": {
                "param_lolmt": 10,
                "param_hglmt": 9007199254740000
            },
            "param_list_sparse": {
                "param_sparse": 0.80
            },
            "param_list_no_change": {
                "param_sparse": 0.80
            }
        }
        
        # Basic test data structure
        self.basic_data = {
            "timestamps": [1, 2, 3, 4, 5],
            "metric_data": {
                "series": [
                    {
                        "metric_val": [10, 20, 30, 40, 50],
                        "val_status": [0, 0, 0, 0, 0]
                    },
                    {
                        "metric_val": [15, 25, 35, 45, 55],
                        "val_status": [0, 0, 0, 0, 0]
                    },
                    {
                        "metric_val": [12, 22, 32, 42, 52],
                        "val_status": [0, 0, 0, 0, 0]
                    }
                ],
                "change_period": False
            },
            "param": {
                "param_valid_interval": {
                    "param_lolmt": 10,
                    "param_hglmt": 9007199254740000
                }
            }
        }
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_basic_functionality(self, mock_default, mock_check_report_status):
        """Test basic functionality with normal input"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        result = data_preprocess(self.basic_data)
        
        # Verify structure
        assert "timestamps" in result
        assert "change_period" in result
        assert "origin_data" in result
        assert "metric_today" in result
        assert "metric_yesterday" in result
        assert "metric_lastweek" in result
        assert "interval" in result
        assert "curve" in result
        assert "is_change_err" in result
        assert "log" in result
        assert "param" in result
        
        # Verify data types and lengths
        assert isinstance(result["timestamps"], list)
        assert isinstance(result["metric_today"], list)
        assert isinstance(result["metric_yesterday"], list)
        assert isinstance(result["metric_lastweek"], list)
        assert len(result["metric_today"]) == 5
        assert len(result["is_change_err"]) == 5
        
        # Verify normalization (values should be between 0 and 1)
        assert all(0 <= val <= 1 for val in result["metric_today"])
        assert all(0 <= val <= 1 for val in result["metric_yesterday"])
        assert all(0 <= val <= 1 for val in result["metric_lastweek"])
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_with_missing_param_valid_interval(self, mock_default, mock_check_report_status):
        """Test with missing param_valid_interval in input"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        # Remove param_valid_interval from input
        test_data = self.basic_data.copy()
        del test_data["param"]["param_valid_interval"]
        
        result = data_preprocess(test_data)
        
        # Should use default values
        assert "log" in result
        assert "in_valid_interval" in result["log"]
        assert isinstance(result["log"]["in_valid_interval"], bool)
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_with_change_period_none(self, mock_default, mock_check_report_status):
        """Test with change_period set to None"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        test_data = self.basic_data.copy()
        test_data["metric_data"]["change_period"] = None
        
        result = data_preprocess(test_data)
        
        assert result["change_period"] is False
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_with_sparse_data(self, mock_default, mock_check_report_status):
        """Test with sparse data (many unreported values)"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        test_data = self.basic_data.copy()
        # Set most values as unreported (1 = unreported)
        test_data["metric_data"]["series"][0]["val_status"] = [1, 1, 1, 0, 0]
        
        result = data_preprocess(test_data)
        
        assert "is_sparse" in result["log"]
        assert "report_ratio" in result["log"]
        assert isinstance(result["log"]["is_sparse"], bool)
        assert isinstance(result["log"]["report_ratio"], (int, float))
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_edge_case_empty_val_status(self, mock_default, mock_check_report_status):
        """Test edge case with empty val_status"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        test_data = self.basic_data.copy()
        test_data["metric_data"]["series"][0]["val_status"] = []
        
        result = data_preprocess(test_data)
        
        # Should handle empty val_status gracefully
        assert "log" in result
        assert "is_sparse" in result["log"]
        assert result["log"]["is_sparse"] is True
        assert result["log"]["report_ratio"] == -1
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_with_identical_values(self, mock_default, mock_check_report_status):
        """Test with identical values (edge case for normalization)"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        test_data = self.basic_data.copy()
        # All values identical
        test_data["metric_data"]["series"][0]["metric_val"] = [25, 25, 25, 25, 25]
        test_data["metric_data"]["series"][1]["metric_val"] = [25, 25, 25, 25, 25]
        test_data["metric_data"]["series"][2]["metric_val"] = [25, 25, 25, 25, 25]
        
        result = data_preprocess(test_data)
        
        # When all values are identical, normalization should result in all zeros
        assert all(val == 0 for val in result["metric_today"])
        assert all(val == 0 for val in result["metric_yesterday"])
        assert all(val == 0 for val in result["metric_lastweek"])
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_with_single_value(self, mock_default, mock_check_report_status):
        """Test with single data point"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        test_data = self.basic_data.copy()
        test_data["timestamps"] = [1]
        test_data["metric_data"]["series"][0]["metric_val"] = [10]
        test_data["metric_data"]["series"][0]["val_status"] = [0]
        test_data["metric_data"]["series"][1]["metric_val"] = [15]
        test_data["metric_data"]["series"][1]["val_status"] = [0]
        test_data["metric_data"]["series"][2]["metric_val"] = [12]
        test_data["metric_data"]["series"][2]["val_status"] = [0]
        
        result = data_preprocess(test_data)
        
        assert len(result["metric_today"]) == 1
        assert len(result["is_change_err"]) == 1
        assert len(result["timestamps"]) == 1
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_with_large_values(self, mock_default, mock_check_report_status):
        """Test with large numerical values"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        test_data = self.basic_data.copy()
        test_data["metric_data"]["series"][0]["metric_val"] = [1e6, 2e6, 3e6, 4e6, 5e6]
        test_data["metric_data"]["series"][1]["metric_val"] = [1.5e6, 2.5e6, 3.5e6, 4.5e6, 5.5e6]
        test_data["metric_data"]["series"][2]["metric_val"] = [1.2e6, 2.2e6, 3.2e6, 4.2e6, 5.2e6]
        
        result = data_preprocess(test_data)
        
        # Should handle large values correctly
        assert all(0 <= val <= 1 for val in result["metric_today"])
        assert all(0 <= val <= 1 for val in result["metric_yesterday"])
        assert all(0 <= val <= 1 for val in result["metric_lastweek"])
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_with_negative_values(self, mock_default, mock_check_report_status):
        """Test with negative values"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.return_value = {"test": "data"}
        
        test_data = self.basic_data.copy()
        test_data["metric_data"]["series"][0]["metric_val"] = [-10, -5, 0, 5, 10]
        test_data["metric_data"]["series"][1]["metric_val"] = [-8, -3, 2, 7, 12]
        test_data["metric_data"]["series"][2]["metric_val"] = [-12, -7, -2, 3, 8]
        
        result = data_preprocess(test_data)
        
        # Should handle negative values correctly
        assert all(0 <= val <= 1 for val in result["metric_today"])
        assert all(0 <= val <= 1 for val in result["metric_yesterday"])
        assert all(0 <= val <= 1 for val in result["metric_lastweek"])


class TestHelperFunctions:
    """Test helper functions used by data_preprocess"""
    
    def test_get_norm_alpha(self):
        """Test get_norm_alpha function"""
        list_list_data = [[1, 2, 3], [4, 5, 6], [0, 7, 2]]
        min_val, max_val = get_norm_alpha(list_list_data)
        assert min_val == 0
        assert max_val == 7
    
    def test_get_norm_alpha_single_list(self):
        """Test get_norm_alpha with single list"""
        list_list_data = [[5, 10, 15]]
        min_val, max_val = get_norm_alpha(list_list_data)
        assert min_val == 5
        assert max_val == 15
    
    def test_norm_list(self):
        """Test norm_list function"""
        list_list_data = [[0, 5, 10], [2, 7, 12]]
        result = norm_list(0, 12, list_list_data)
        
        expected = [[0.0, 5/12, 10/12], [2/12, 7/12, 1.0]]
        assert result == expected
    
    def test_norm_list_with_zeros(self):
        """Test norm_list with zero values"""
        list_list_data = [[0, 0, 10], [0, 5, 0]]
        result = norm_list(0, 10, list_list_data)
        
        expected = [[0, 0, 1.0], [0, 0.5, 0]]
        assert result == expected
    
    def test_ckeck_in_valid_interval(self):
        """Test ckeck_in_valid_interval function"""
        metric_today = [5, 15, 25, 35, 45]
        
        # Test case where values are in interval
        assert ckeck_in_valid_interval(metric_today, 10, 50) is True
        
        # Test case where values are outside interval
        assert ckeck_in_valid_interval(metric_today, 50, 100) is False
        
        # Test with extreme limits
        assert ckeck_in_valid_interval(metric_today, -9007199254740000, 9007199254740000) is True
    
    def test_is_sparse(self):
        """Test is_sparse function"""
        # Test normal case
        data = {
            "metric_data": {
                "series": [
                    {
                        "metric_val": [1, 2, 3, 4, 5],
                        "val_status": [0, 0, 1, 1, 0]  # 2 out of 5 unreported
                    }
                ]
            }
        }
        
        is_sparse_result, ratio = is_sparse(data, param_sparse=0.10)
        assert is_sparse_result is False  # 60% reported > 10% threshold
        assert ratio == 0.6
        
        # Test sparse case
        is_sparse_result, ratio = is_sparse(data, param_sparse=0.70)
        assert is_sparse_result is True  # 60% reported < 70% threshold
        
        # Test empty val_status
        data_empty = {
            "metric_data": {
                "series": [
                    {
                        "metric_val": [1, 2, 3],
                        "val_status": []
                    }
                ]
            }
        }
        
        is_sparse_result, ratio = is_sparse(data_empty)
        assert is_sparse_result is True
        assert ratio == -1


if __name__ == "__main__":
    pytest.main([__file__])
