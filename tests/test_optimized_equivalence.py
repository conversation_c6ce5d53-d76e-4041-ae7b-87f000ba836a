import pytest
import numpy as np
import pandas as pd
import sys
import os
import copy
from unittest.mock import patch, MagicMock

# Add paths
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'cos_ai_service'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'optimized'))

# Import original functions
from vis_data_preprocess import data_preprocess as data_preprocess_original
from model.data_preprocess import downsample as downsample_original, smooth_ewma as smooth_ewma_original
from model.model_static.outlier import analysis_nsigma as analysis_nsigma_original, analysis_nsigma_sparse as analysis_nsigma_sparse_original

# Import optimized functions
from vis_data_preprocess_optimized import (
    data_preprocess_optimized, downsample_optimized, smooth_ewma_optimized,
    analysis_nsigma_optimized, analysis_nsigma_sparse_optimized
)


class TestOptimizedEquivalence:
    """Test that optimized functions produce identical results to original functions"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Mock default values for data_preprocess tests
        self.mock_default = {
            "param_list": {
                "param_sparse": 0.80,
                "param_percent": 0.15,
                "param_smooth": 8,
                "param_dtw": 0.18,
                "param_downsample": 360,
                "param_dtw_low": 1.2,
                "param_dtw_high": 2.4,
                "param_sigma": 3,
                "param_cos_sim": 0.35,
                "time_window_focus": 3,
                "time_window_dtw": 240,
                "time_window_nsigma": 45
            },
            "param_valid_interval": {
                "param_lolmt": 10,
                "param_hglmt": 9007199254740000
            },
            "param_list_sparse": {
                "param_sparse": 0.80
            },
            "param_list_no_change": {
                "param_sparse": 0.80
            }
        }
        
        # Test data for data_preprocess
        self.data_preprocess_test_data = {
            "timestamps": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            "metric_data": {
                "series": [
                    {
                        "metric_val": [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
                        "val_status": [0, 0, 0, 0, 0, 1, 1, 0, 0, 0]
                    },
                    {
                        "metric_val": [15, 25, 35, 45, 55, 65, 75, 85, 95, 105],
                        "val_status": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    },
                    {
                        "metric_val": [12, 22, 32, 42, 52, 62, 72, 82, 92, 102],
                        "val_status": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
                    }
                ],
                "change_period": False
            },
            "param": {
                "param_valid_interval": {
                    "param_lolmt": 10,
                    "param_hglmt": 9007199254740000
                }
            }
        }
        
        # Test data for downsample
        self.downsample_test_data = {
            "metric_today": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
            "metric_yesterday": [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22],
            "metric_lastweek": [21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32]
        }
        
        # Test data for smooth_ewma
        self.smooth_ewma_test_data = {
            "metric_today": [1.0, 2.0, 3.0, 4.0, 5.0] * 300,  # 1500 points
            "metric_yesterday": [1.5, 2.5, 3.5, 4.5, 5.5] * 300,
            "metric_lastweek": [1.2, 2.2, 3.2, 4.2, 5.2] * 300
        }
        
        # Test data for nsigma functions
        self.nsigma_test_data = {
            "metric_today": [10.0, 12.0, 11.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0],
            "metric_yesterday": [9.0, 11.0, 10.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0],
            "metric_lastweek": [8.0, 10.0, 9.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0],
            "is_change_err": [1] * 10,
            "param": {
                "param_list": {
                    "time_window_focus": 3,
                    "param_sigma": 3
                }
            },
            "interval": {},
            "log": {},
            "origin_data": {
                "metric_today_report_status": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                "metric_yesterday_report_status": [0] * 1440,
                "metric_lastweek_report_status": [0] * 1440
            }
        }
    
    def assert_deep_equal(self, obj1, obj2, path=""):
        """Deep comparison of two objects with detailed error reporting"""
        if type(obj1) != type(obj2):
            raise AssertionError(f"Type mismatch at {path}: {type(obj1)} vs {type(obj2)}")
        
        if isinstance(obj1, dict):
            assert set(obj1.keys()) == set(obj2.keys()), f"Key mismatch at {path}"
            for key in obj1.keys():
                self.assert_deep_equal(obj1[key], obj2[key], f"{path}.{key}")
        elif isinstance(obj1, list):
            assert len(obj1) == len(obj2), f"Length mismatch at {path}: {len(obj1)} vs {len(obj2)}"
            for i, (item1, item2) in enumerate(zip(obj1, obj2)):
                self.assert_deep_equal(item1, item2, f"{path}[{i}]")
        elif isinstance(obj1, (int, float)):
            if isinstance(obj1, float) or isinstance(obj2, float):
                assert abs(obj1 - obj2) < 1e-10, f"Float mismatch at {path}: {obj1} vs {obj2}"
            else:
                assert obj1 == obj2, f"Value mismatch at {path}: {obj1} vs {obj2}"
        else:
            assert obj1 == obj2, f"Value mismatch at {path}: {obj1} vs {obj2}"
    
    @patch('vis_data_preprocess.check_report_status')
    @patch('model.utils.default.default')
    def test_data_preprocess_equivalence(self, mock_default, mock_check_report_status):
        """Test that optimized data_preprocess produces identical results"""
        mock_default.__getitem__.side_effect = lambda key: self.mock_default[key]
        mock_check_report_status.side_effect = lambda x: x  # Return unchanged
        
        # Test with multiple scenarios
        test_cases = [
            self.data_preprocess_test_data,
            # Add edge case with identical values
            {
                **self.data_preprocess_test_data,
                "metric_data": {
                    **self.data_preprocess_test_data["metric_data"],
                    "series": [
                        {"metric_val": [25] * 5, "val_status": [0] * 5},
                        {"metric_val": [25] * 5, "val_status": [0] * 5},
                        {"metric_val": [25] * 5, "val_status": [0] * 5}
                    ]
                }
            }
        ]
        
        for i, test_data in enumerate(test_cases):
            with patch('vis_data_preprocess_optimized.check_report_status', side_effect=lambda x: x):
                original_result = data_preprocess_original(copy.deepcopy(test_data))
                optimized_result = data_preprocess_optimized(copy.deepcopy(test_data))
                
                self.assert_deep_equal(original_result, optimized_result, f"test_case_{i}")
    
    def test_downsample_equivalence(self):
        """Test that optimized downsample produces identical results"""
        test_cases = [
            (self.downsample_test_data, 5),
            (self.downsample_test_data, 3),
            (self.downsample_test_data, 1),
            (self.downsample_test_data, 20),  # target_len > data length
        ]
        
        for i, (test_data, target_len) in enumerate(test_cases):
            original_result = downsample_original(copy.deepcopy(test_data), target_len)
            optimized_result = downsample_optimized(copy.deepcopy(test_data), target_len)
            
            self.assert_deep_equal(original_result, optimized_result, f"downsample_test_case_{i}")
    
    def test_smooth_ewma_equivalence(self):
        """Test that optimized smooth_ewma produces identical results"""
        test_cases = [
            (self.smooth_ewma_test_data, 0.3, 240, 3),
            (self.smooth_ewma_test_data, 0.1, 100, 5),
            (self.smooth_ewma_test_data, 0.8, 500, 1),
        ]
        
        for i, (test_data, param_smooth, time_window, time_window_focus) in enumerate(test_cases):
            original_result = smooth_ewma_original(
                copy.deepcopy(test_data), param_smooth, time_window, time_window_focus
            )
            optimized_result = smooth_ewma_optimized(
                copy.deepcopy(test_data), param_smooth, time_window, time_window_focus
            )
            
            self.assert_deep_equal(original_result, optimized_result, f"smooth_ewma_test_case_{i}")
    
    def test_analysis_nsigma_equivalence(self):
        """Test that optimized analysis_nsigma produces identical results"""
        test_cases = [
            (self.nsigma_test_data, 2, 240),
            (self.nsigma_test_data, 3, 100),
            (self.nsigma_test_data, 1, 500),
        ]
        
        for i, (test_data, param_sigma, time_window) in enumerate(test_cases):
            original_result = analysis_nsigma_original(
                copy.deepcopy(test_data), param_sigma, time_window
            )
            optimized_result = analysis_nsigma_optimized(
                copy.deepcopy(test_data), param_sigma, time_window
            )
            
            self.assert_deep_equal(original_result, optimized_result, f"analysis_nsigma_test_case_{i}")
    
    def test_analysis_nsigma_sparse_equivalence(self):
        """Test that optimized analysis_nsigma_sparse produces identical results"""
        # Prepare test data with proper structure for sparse analysis
        sparse_test_data = copy.deepcopy(self.nsigma_test_data)
        sparse_test_data["metric_yesterday"] = [9.0, 11.0, 10.0, 12.0, 13.0] + [0.0] * 1435
        sparse_test_data["metric_lastweek"] = [8.0, 10.0, 9.0, 11.0, 12.0] * 288  # 1440 total
        
        test_cases = [
            (sparse_test_data, 2, -1),
            (sparse_test_data, 3, 0),  # Should return unchanged
            (sparse_test_data, 1, 100),
        ]
        
        for i, (test_data, param_sigma, time_window) in enumerate(test_cases):
            # Capture print output for comparison
            import io
            import contextlib
            
            original_output = io.StringIO()
            optimized_output = io.StringIO()
            
            with contextlib.redirect_stdout(original_output):
                original_result = analysis_nsigma_sparse_original(
                    copy.deepcopy(test_data), param_sigma, time_window
                )
            
            with contextlib.redirect_stdout(optimized_output):
                optimized_result = analysis_nsigma_sparse_optimized(
                    copy.deepcopy(test_data), param_sigma, time_window
                )
            
            # Compare results
            self.assert_deep_equal(original_result, optimized_result, f"analysis_nsigma_sparse_test_case_{i}")
            
            # Compare print outputs
            assert original_output.getvalue() == optimized_output.getvalue(), \
                f"Print output mismatch in test case {i}"
    
    def test_edge_cases_equivalence(self):
        """Test edge cases for all functions"""
        # Test with single element
        single_element_data = {
            "metric_today": [42],
            "metric_yesterday": [43],
            "metric_lastweek": [44]
        }
        
        original_result = downsample_original(copy.deepcopy(single_element_data), 1)
        optimized_result = downsample_optimized(copy.deepcopy(single_element_data), 1)
        self.assert_deep_equal(original_result, optimized_result, "single_element_downsample")
        
        # Test with negative values
        negative_data = {
            "metric_today": [-5, -3, -1, 1, 3, 5],
            "metric_yesterday": [-10, -8, -6, -4, -2, 0],
            "metric_lastweek": [-15, -12, -9, -6, -3, 0]
        }
        
        original_result = downsample_original(copy.deepcopy(negative_data), 3)
        optimized_result = downsample_optimized(copy.deepcopy(negative_data), 3)
        self.assert_deep_equal(original_result, optimized_result, "negative_values_downsample")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
