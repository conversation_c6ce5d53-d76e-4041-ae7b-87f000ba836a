import pytest
import numpy as np
import sys
import os
from unittest.mock import patch
from io import StringIO

# Add the cos_ai_service directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'cos_ai_service'))

from model.model_static.outlier import analysis_nsigma, analysis_nsigma_sparse, get_nsigma_cal_res


class TestAnalysisNsigma:
    """Comprehensive unit tests for analysis_nsigma function"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.basic_data_dic = {
            "metric_today": [10.0, 12.0, 11.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0, 19.0],
            "metric_yesterday": [9.0, 11.0, 10.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0, 18.0],
            "metric_lastweek": [8.0, 10.0, 9.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0, 17.0],
            "is_change_err": [1] * 10,  # Initially all marked as errors
            "param": {
                "param_list": {
                    "time_window_focus": 3,
                    "param_sigma": 3
                }
            },
            "interval": {},
            "log": {}
        }
    
    def test_analysis_nsigma_basic_functionality(self):
        """Test basic n-sigma analysis functionality"""
        result = analysis_nsigma(self.basic_data_dic.copy(), param_sigma=2, time_window=240)
        
        # Verify structure
        assert "interval" in result
        assert "nsigma" in result["interval"]
        assert "highlimit" in result["interval"]["nsigma"]
        assert "lowlimit" in result["interval"]["nsigma"]
        assert "log" in result
        assert "nsigma" in result["log"]
        
        # Verify lengths
        assert len(result["interval"]["nsigma"]["highlimit"]) == 10
        assert len(result["interval"]["nsigma"]["lowlimit"]) == 10
        assert len(result["log"]["nsigma"]) == 10
        
        # Verify that limits are calculated correctly
        highlimit = result["interval"]["nsigma"]["highlimit"]
        lowlimit = result["interval"]["nsigma"]["lowlimit"]
        
        # Most values should be inf/-inf except for the focus window
        focus_start = max(0, 10 - 3)  # time_window_focus = 3
        assert all(hl == float("inf") for hl in highlimit[:focus_start])
        assert all(ll == float("-inf") for ll in lowlimit[:focus_start])
        
        # Focus window should have calculated limits
        assert all(hl != float("inf") for hl in highlimit[focus_start:])
        assert all(ll != float("-inf") for ll in lowlimit[focus_start:])
    
    def test_analysis_nsigma_normal_values_within_limits(self):
        """Test that normal values are marked as non-errors"""
        # Create data where today's values are similar to historical
        data_dic = {
            "metric_today": [10.0, 10.1, 9.9, 10.2, 9.8],
            "metric_yesterday": [10.0, 10.0, 10.0, 10.0, 10.0],
            "metric_lastweek": [10.0, 10.0, 10.0, 10.0, 10.0],
            "is_change_err": [1] * 5,
            "param": {
                "param_list": {
                    "time_window_focus": 3,
                    "param_sigma": 3
                }
            },
            "interval": {},
            "log": {}
        }
        
        result = analysis_nsigma(data_dic, param_sigma=3, time_window=240)
        
        # Values within 3 sigma should be marked as normal (0)
        focus_start = max(0, 5 - 3)
        for i in range(focus_start, 5):
            assert result["is_change_err"][i] == 0
            assert result["log"]["nsigma"][i] == -2  # -2 indicates normal
    
    def test_analysis_nsigma_outlier_detection(self):
        """Test outlier detection"""
        # Create data with clear outliers
        data_dic = {
            "metric_today": [10.0, 10.0, 10.0, 100.0, 10.0],  # 100.0 is outlier
            "metric_yesterday": [10.0, 10.0, 10.0, 10.0, 10.0],
            "metric_lastweek": [10.0, 10.0, 10.0, 10.0, 10.0],
            "is_change_err": [1] * 5,
            "param": {
                "param_list": {
                    "time_window_focus": 3,
                    "param_sigma": 2
                }
            },
            "interval": {},
            "log": {}
        }
        
        result = analysis_nsigma(data_dic, param_sigma=2, time_window=240)
        
        # The outlier should be detected
        focus_start = max(0, 5 - 3)
        outlier_idx = 3
        if outlier_idx >= focus_start:
            assert result["log"]["nsigma"][outlier_idx] > 0  # Positive value indicates outlier
    
    def test_analysis_nsigma_zero_variance(self):
        """Test behavior with zero variance (identical values)"""
        data_dic = {
            "metric_today": [10.0, 10.0, 10.0, 10.0, 10.0],
            "metric_yesterday": [10.0, 10.0, 10.0, 10.0, 10.0],
            "metric_lastweek": [10.0, 10.0, 10.0, 10.0, 10.0],
            "is_change_err": [1] * 5,
            "param": {
                "param_list": {
                    "time_window_focus": 3,
                    "param_sigma": 3
                }
            },
            "interval": {},
            "log": {}
        }
        
        result = analysis_nsigma(data_dic, param_sigma=3, time_window=240)
        
        # With zero variance, all values should be considered normal
        focus_start = max(0, 5 - 3)
        for i in range(focus_start, 5):
            assert result["is_change_err"][i] == 0
    
    def test_analysis_nsigma_different_sigma_values(self):
        """Test with different sigma values"""
        data_dic = {
            "metric_today": [10.0, 10.0, 10.0, 15.0, 10.0],  # 15.0 is potential outlier
            "metric_yesterday": [10.0, 10.0, 10.0, 10.0, 10.0],
            "metric_lastweek": [10.0, 10.0, 10.0, 10.0, 10.0],
            "is_change_err": [1] * 5,
            "param": {
                "param_list": {
                    "time_window_focus": 5,
                    "param_sigma": 1
                }
            },
            "interval": {},
            "log": {}
        }
        
        # Test with strict sigma (1)
        result_strict = analysis_nsigma(data_dic.copy(), param_sigma=1, time_window=240)
        
        # Test with lenient sigma (5)
        result_lenient = analysis_nsigma(data_dic.copy(), param_sigma=5, time_window=240)
        
        # Strict sigma should detect more outliers than lenient sigma
        strict_outliers = sum(1 for val in result_strict["log"]["nsigma"] if val > 0)
        lenient_outliers = sum(1 for val in result_lenient["log"]["nsigma"] if val > 0)
        
        assert strict_outliers >= lenient_outliers


class TestAnalysisNsigmaSparse:
    """Comprehensive unit tests for analysis_nsigma_sparse function"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.basic_data_dic = {
            "metric_today": [10.0, 12.0, 11.0, 13.0, 14.0],
            "metric_yesterday": [9.0, 11.0, 10.0, 12.0, 13.0] + [0.0] * 1435,  # Pad to 1440
            "metric_lastweek": [8.0, 10.0, 9.0, 11.0, 12.0] * 288,  # 1440 points
            "origin_data": {
                "metric_today_report_status": [0, 0, 0, 0, 0],  # 0 = reported
                "metric_yesterday_report_status": [0, 0, 0, 0, 0] + [1] * 1435,  # 1 = not reported
                "metric_lastweek_report_status": [0, 0, 0, 0, 0] * 288
            },
            "is_change_err": [1] * 5,
            "param": {
                "param_list": {
                    "param_sigma": 3
                }
            },
            "interval": {},
            "log": {}
        }
    
    def test_analysis_nsigma_sparse_disabled(self):
        """Test when time_window is 0 (disabled)"""
        result = analysis_nsigma_sparse(self.basic_data_dic.copy(), param_sigma=3, time_window=0)
        
        # Should return unchanged data
        assert result == self.basic_data_dic
    
    def test_analysis_nsigma_sparse_basic_functionality(self):
        """Test basic sparse n-sigma analysis functionality"""
        result = analysis_nsigma_sparse(self.basic_data_dic.copy(), param_sigma=2, time_window=-1)
        
        # Verify structure
        assert "interval" in result
        assert "nsigma" in result["interval"]
        assert "highlimit" in result["interval"]["nsigma"]
        assert "lowlimit" in result["interval"]["nsigma"]
        assert "log" in result
        assert "nsigma" in result["log"]
        
        # Verify lengths
        assert len(result["interval"]["nsigma"]["highlimit"]) == 5
        assert len(result["interval"]["nsigma"]["lowlimit"]) == 5
        assert len(result["log"]["nsigma"]) == 5
    
    def test_analysis_nsigma_sparse_unreported_values(self):
        """Test handling of unreported values"""
        data_dic = self.basic_data_dic.copy()
        # Mark some values as unreported
        data_dic["origin_data"]["metric_today_report_status"] = [1, 0, 1, 0, 0]  # 1 = unreported
        
        result = analysis_nsigma_sparse(data_dic, param_sigma=3, time_window=-1)
        
        # Unreported values should be marked as normal (0)
        assert result["is_change_err"][0] == 0  # Unreported
        assert result["is_change_err"][2] == 0  # Unreported
    
    def test_analysis_nsigma_sparse_all_unreported(self):
        """Test when all historical data is unreported"""
        data_dic = self.basic_data_dic.copy()
        # Mark all historical data as unreported
        data_dic["origin_data"]["metric_yesterday_report_status"] = [1] * 1440
        data_dic["origin_data"]["metric_lastweek_report_status"] = [1] * 1440
        data_dic["origin_data"]["metric_today_report_status"] = [1, 1, 1, 0, 0]  # Only last 2 reported
        
        result = analysis_nsigma_sparse(data_dic, param_sigma=3, time_window=-1)
        
        # Should handle gracefully
        assert "interval" in result
        assert "nsigma" in result["interval"]
    
    @patch('sys.stdout', new_callable=StringIO)
    def test_analysis_nsigma_sparse_outlier_detection_with_print(self, mock_stdout):
        """Test outlier detection with print output"""
        # Create data with clear outlier
        data_dic = {
            "metric_today": [10.0, 100.0],  # 100.0 is outlier
            "metric_yesterday": [10.0] * 1440,
            "metric_lastweek": [10.0] * 1440,
            "origin_data": {
                "metric_today_report_status": [0, 0],  # Both reported
                "metric_yesterday_report_status": [0] * 1440,
                "metric_lastweek_report_status": [0] * 1440
            },
            "is_change_err": [1, 1],
            "param": {
                "param_list": {
                    "param_sigma": 2
                }
            },
            "interval": {},
            "log": {}
        }
        
        result = analysis_nsigma_sparse(data_dic, param_sigma=2, time_window=-1)
        
        # Should detect outlier and print warning
        output = mock_stdout.getvalue()
        assert "nsigma告警" in output or result["log"]["nsigma"][1] > 0
    
    def test_analysis_nsigma_sparse_incremental_learning(self):
        """Test incremental learning behavior"""
        data_dic = {
            "metric_today": [10.0, 10.1, 9.9, 10.2],
            "metric_yesterday": [10.0] * 1440,
            "metric_lastweek": [10.0] * 1440,
            "origin_data": {
                "metric_today_report_status": [0, 0, 0, 0],
                "metric_yesterday_report_status": [0] * 1440,
                "metric_lastweek_report_status": [0] * 1440
            },
            "is_change_err": [1, 1, 1, 1],
            "param": {
                "param_list": {
                    "param_sigma": 3
                }
            },
            "interval": {},
            "log": {}
        }
        
        result = analysis_nsigma_sparse(data_dic, param_sigma=3, time_window=-1)
        
        # Limits should be updated incrementally
        limits = result["interval"]["nsigma"]
        assert len(limits["highlimit"]) == 4
        assert len(limits["lowlimit"]) == 4
        
        # Later limits should incorporate earlier today's values
        # (This tests the incremental learning aspect)
        assert limits["highlimit"][3] != limits["highlimit"][0]


class TestGetNsigmaCalRes:
    """Test helper function get_nsigma_cal_res"""
    
    def test_get_nsigma_cal_res_basic(self):
        """Test basic n-sigma calculation"""
        # Test case: value=15, limits=[5,25], sigma=3
        # avg = (5+25)/2 = 15, std = (25-5)/2/3 = 10/6 = 5/3
        # deviation = |15-15| / (5/3) = 0
        result = get_nsigma_cal_res(15, 5, 25, 3)
        assert abs(result - 0) < 1e-10
    
    def test_get_nsigma_cal_res_outlier(self):
        """Test with outlier value"""
        # Test case: value=30, limits=[10,20], sigma=2
        # avg = 15, std = 10/2/2 = 2.5
        # deviation = |30-15| / 2.5 = 15/2.5 = 6
        result = get_nsigma_cal_res(30, 10, 20, 2)
        assert abs(result - 6.0) < 1e-10
    
    def test_get_nsigma_cal_res_zero_std(self):
        """Test with zero standard deviation"""
        # When high_limit == low_limit, std = 0
        result = get_nsigma_cal_res(10, 15, 15, 3)
        assert result == -1
    
    def test_get_nsigma_cal_res_negative_values(self):
        """Test with negative values"""
        # Test case: value=-5, limits=[-15,5], sigma=2
        # avg = -5, std = 20/2/2 = 5
        # deviation = |-5-(-5)| / 5 = 0
        result = get_nsigma_cal_res(-5, -15, 5, 2)
        assert abs(result - 0) < 1e-10


if __name__ == "__main__":
    pytest.main([__file__])
