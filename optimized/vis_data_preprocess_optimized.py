import numpy as np
import pandas as pd


def get_norm_alpha_optimized(list_list_data):
    """Optimized version using numpy for min/max calculation"""
    all_data = np.concatenate(list_list_data)
    return float(np.min(all_data)), float(np.max(all_data))


def norm_list_optimized(min_val, max_val, list_list_data):
    """Optimized version using numpy vectorized operations"""
    if max_val == min_val:
        # Handle edge case where all values are identical
        return [[0.0 if val != 0 else 0.0 for val in list_data] for list_data in list_list_data]
    
    result = []
    for list_data in list_list_data:
        arr = np.array(list_data, dtype=float)
        # Vectorized normalization with special handling for zeros
        normalized = np.where(arr != 0, (arr - min_val) / (max_val - min_val), 0.0)
        result.append(normalized.tolist())
    
    return result


def ckeck_in_valid_interval_optimized(metric_today, lolmt, hglmt, time_window_focus=3):
    """Optimized version using numpy array operations"""
    if len(metric_today) == 0:
        return False
    
    # Get the last time_window_focus values
    t = np.array(metric_today[-time_window_focus:])
    
    # Check conditions using vectorized operations
    cond1 = (lolmt > -9007199254740000) & (hglmt < 9007199254740000) & (t > lolmt) & (t < hglmt)
    cond2 = (lolmt > -9007199254740000) & (t > lolmt)
    cond3 = (hglmt < 9007199254740000) & (t < hglmt)
    
    return bool(np.any(cond1 | cond2 | cond3))


def is_sparse_optimized(data, param_sparse=0.10):
    """Optimized version using numpy for report ratio calculation"""
    series = data["metric_data"]["series"]
    mtoday, mt_report = series[0]["metric_val"], series[0]["val_status"]
    
    if len(mt_report) == 0:
        return True, -1
    
    # Use numpy for efficient counting
    mt_report_arr = np.array(mt_report)
    report_num = len(mt_report_arr) - np.sum(mt_report_arr)
    report_ratio = report_num / max(1, len(mtoday))
    
    if report_ratio > param_sparse:
        return False, report_ratio
    return True, report_ratio


def check_param_optimized(metric_data):
    """Optimized version - same logic but cleaner structure"""
    from model.utils.default import default
    
    param_defaults = {
        "param_list": default["param_list"],
        "param_valid_interval": default["param_valid_interval"],
        "param_list_sparse": default["param_list_sparse"],
        "param_list_no_change": default["param_list_no_change"]
    }
    
    for key, default_value in param_defaults.items():
        if key not in metric_data["param"]:
            metric_data["param"][key] = default_value
    
    return metric_data


def data_preprocess_optimized(data):
    """
    Optimized version of data_preprocess function using numpy/pandas operations.
    Maintains exact functional equivalence with the original implementation.
    """
    # Check and set default parameters
    data = check_param_optimized(data)
    
    # Extract series data
    series = data["metric_data"]["series"]
    metric_today = series[0]["metric_val"]
    metric_yesterday = series[1]["metric_val"] 
    metric_lastweek = series[2]["metric_val"]
    
    # Get normalization parameters using optimized function
    minval, maxval = get_norm_alpha_optimized([metric_today, metric_yesterday, metric_lastweek])
    
    # Normalize data using optimized function
    list_list_data = [metric_today, metric_yesterday, metric_lastweek]
    list_list_data = norm_list_optimized(minval, maxval, list_list_data)
    
    # Get parameters
    param = data["param"]
    
    # Import default values
    from model.utils.default import figure_size, default
    
    # Check param_valid_interval
    if "param_valid_interval" not in param:
        print("data_preprocess: 'param_valid_interval' not found in param, using default values.")
        param_valid_interval = default["param_valid_interval"]
    else:
        param_valid_interval = param["param_valid_interval"]
    
    # Check if in valid interval using optimized function
    in_valid_interval = ckeck_in_valid_interval_optimized(
        series[0]["metric_val"],
        param_valid_interval["param_lolmt"],
        param_valid_interval["param_hglmt"]
    )
    
    # Check if sparse using optimized function
    issparse, report_ratio = is_sparse_optimized(data, param_sparse=0.10)
    
    # Build result dictionary
    data_dic = {
        "timestamps": list(data["timestamps"]),
        "change_period": data["metric_data"]["change_period"] if data["metric_data"]["change_period"] is not None else False,
        "origin_data": {
            "metric_today": series[0]["metric_val"],
            "metric_today_report_status": series[0]['val_status'],
            "metric_yesterday": series[1]["metric_val"],
            "metric_yesterday_report_status": series[1]['val_status'],
            "metric_lastweek": series[2]["metric_val"],
            "metric_lastweek_report_status": series[2]['val_status']
        },
        "metric_today": list_list_data[0],
        "metric_yesterday": list_list_data[1],
        "metric_lastweek": list_list_data[2],
        "interval": {},
        "curve": {"high": {}, "low": {}},
        "is_change_err": [0] * len(metric_today),
        "log": {
            "is_sparse": issparse,
            "report_ratio": report_ratio,
            "in_valid_interval": in_valid_interval
        },
        "param": param
    }
    
    # Apply check_report_status (import and call the original function)
    from model.data_preprocess import check_report_status
    data_dic = check_report_status(data_dic)
    
    return data_dic


def downsample_optimized(data, target_len):
    """
    Optimized version of downsample function using numpy operations.
    Maintains exact functional equivalence with the original implementation.
    """
    def ds_optimized(d, target_len):
        """Optimized downsampling helper using numpy"""
        if target_len >= len(d):
            return data  # Return original data dict (matches original behavior)
        
        d_arr = np.array(d)
        len_interval = len(d) / target_len
        
        # Vectorized interval calculation
        starts = np.floor(np.arange(target_len) * len_interval).astype(int)
        ends = np.floor(np.arange(1, target_len + 1) * len_interval).astype(int)
        ends[-1] = len(d)  # Ensure last interval goes to end
        
        # Calculate max for each interval
        result = []
        for start, end in zip(starts, ends):
            result.append(float(np.max(d_arr[start:end])))
        
        return result
    
    # Apply downsampling to each metric
    data["downsample"] = {
        "metric_today": ds_optimized(data["metric_today"], target_len),
        "metric_yesterday": ds_optimized(data["metric_yesterday"], target_len),
        "metric_lastweek": ds_optimized(data["metric_lastweek"], target_len)
    }
    
    return data


def smooth_ewma_optimized(data_dic, param_smooth=0.3, time_window=24 * 60, time_window_focus=3):
    """
    Optimized version of smooth_ewma function using pandas EWMA.
    Maintains exact functional equivalence with the original implementation.
    """
    # Extract metric data
    metric_today = data_dic["metric_today"]
    metric_yesterday = data_dic["metric_yesterday"] 
    metric_lastweek = data_dic["metric_lastweek"]
    
    # Calculate window parameters
    time_window_ewma = 3 * 60 + time_window + time_window_focus
    ini_focus = max(0, len(metric_today) - time_window_ewma)
    
    # Create pandas Series for the focus window
    series_to = pd.Series(metric_today[ini_focus:])
    series_yes = pd.Series(metric_yesterday[ini_focus:])
    series_l = pd.Series(metric_lastweek[ini_focus:])
    
    # Apply EWMA smoothing
    sm_to = series_to.ewm(span=param_smooth).mean().tolist()
    sm_yes = series_yes.ewm(span=param_smooth).mean().tolist()
    sm_l = series_l.ewm(span=param_smooth).mean().tolist()
    
    # Calculate the replacement length
    t = time_window + time_window_focus
    
    # Build smoothed data by replacing the end portion
    data_dic["smooth"] = {
        "ewma": {
            "metric_today": metric_today[:-t] + sm_to[-t:],
            "metric_yesterday": metric_yesterday[:-t] + sm_yes[-t:],
            "metric_lastweek": metric_lastweek[:-t] + sm_l[-t:]
        }
    }
    
    return data_dic


def analysis_nsigma_optimized(data_dic, param_sigma, time_window):
    """
    Optimized version of analysis_nsigma function using numpy operations.
    Maintains exact functional equivalence with the original implementation.
    """
    # Extract metric data
    metric_today = np.array(data_dic["metric_today"])
    metric_yesterday = np.array(data_dic["metric_yesterday"])
    metric_lastweek = np.array(data_dic["metric_lastweek"])
    
    # Initialize limits
    highlimit = np.full(len(metric_today), float("inf"))
    lowlimit = np.full(len(metric_today), float("-inf"))
    
    # Get focus window parameters
    twf = data_dic["param"]["param_list"]["time_window_focus"]
    ini_focus = max(0, len(metric_today) - twf)
    
    # Calculate statistics for focus window using numpy
    avg_yesterday = float(np.mean(metric_yesterday[ini_focus:]))
    std_yesterday = float(np.std(metric_yesterday[ini_focus:]))
    avg_lastweek = float(np.mean(metric_lastweek[ini_focus:]))
    std_lastweek = float(np.std(metric_lastweek[ini_focus:]))
    
    # Calculate limits
    lo_yes = avg_yesterday - param_sigma * std_yesterday
    hg_yes = avg_yesterday + param_sigma * std_yesterday
    lo_week = avg_lastweek - param_sigma * std_lastweek
    hg_week = avg_lastweek + param_sigma * std_lastweek
    lo_lmt = min(lo_yes, lo_week)
    hg_lmt = max(hg_yes, hg_week)
    
    # Initialize log
    data_dic["log"]["nsigma"] = [0] * len(metric_today)
    
    # Process focus window
    for i in range(ini_focus, len(metric_today)):
        if lo_lmt <= metric_today[i] <= hg_lmt:
            data_dic["is_change_err"][i] = 0
            data_dic["log"]["nsigma"][i] = -2
        else:
            # Import the helper function for consistency
            from model.model_static.outlier import get_nsigma_cal_res
            data_dic["log"]["nsigma"][i] = get_nsigma_cal_res(
                metric_today[i], lo_lmt, hg_lmt,
                data_dic["param"]["param_list"]["param_sigma"]
            )
        
        lowlimit[i] = lo_lmt
        highlimit[i] = hg_lmt
    
    # Set interval data
    data_dic["interval"]["nsigma"] = {
        "highlimit": highlimit.tolist(),
        "lowlimit": lowlimit.tolist()
    }
    
    return data_dic


def analysis_nsigma_sparse_optimized(data_dic, param_sigma, time_window=-1):
    """
    Optimized version of analysis_nsigma_sparse function using numpy operations.
    Maintains exact functional equivalence with the original implementation.
    """
    if time_window == 0:
        return data_dic
    
    # Convert to numpy arrays for efficient operations
    metric_today = np.array(data_dic["metric_today"])
    metric_lastweek = np.array(data_dic["metric_lastweek"])
    metric_yesterday_1440 = np.array(data_dic["metric_yesterday"][:1440])
    
    # Combine historical data
    metric_tmp = np.concatenate([metric_lastweek, metric_yesterday_1440])
    report_tmp = np.concatenate([
        data_dic["origin_data"]["metric_lastweek_report_status"],
        data_dic["origin_data"]["metric_yesterday_report_status"][:1440]
    ])
    
    # Initialize arrays
    highlimit = np.zeros(len(metric_today))
    lowlimit = np.zeros(len(metric_today))
    
    if time_window == -1:
        time_window = 0
    
    lo_lmt = hg_lmt = metric_today[0]
    data_dic["log"]["nsigma"] = [0] * len(metric_today)
    
    # Process each point
    for i in range(len(metric_today)):
        # Check if all three sources are unreported
        today_unreported = data_dic["origin_data"]["metric_today_report_status"][i] == 1
        yesterday_unreported = data_dic["origin_data"]["metric_yesterday_report_status"][i] == 1
        lastweek_unreported = data_dic["origin_data"]["metric_lastweek_report_status"][i] == 1
        
        if today_unreported and yesterday_unreported and lastweek_unreported:
            highlimit[i] = highlimit[i-1] if i > 0 else 0
            lowlimit[i] = lowlimit[i-1] if i > 0 else 0
            data_dic["is_change_err"][i] = 0
            continue
        
        # Build cumulative valid data using numpy
        metric_cumulative = np.concatenate([metric_tmp, metric_today[:i]])
        report_cumulative = np.concatenate([
            report_tmp, 
            data_dic["origin_data"]["metric_today_report_status"][:i]
        ])
        
        # Filter valid data (where report status is 0)
        valid_mask = np.array(report_cumulative) == 0
        metric_valid = metric_cumulative[valid_mask]
        
        if len(metric_valid) > 0:
            avg = float(np.mean(metric_valid))
            std = float(np.std(metric_valid))
            highlimit[i] = avg + param_sigma * std
            lowlimit[i] = avg - param_sigma * std
            lo_lmt = avg - param_sigma * std
            hg_lmt = avg + param_sigma * std
        
        # Skip comparison if today's value is unreported
        if today_unreported:
            data_dic["is_change_err"][i] = 0
            continue
        
        # Check if within limits
        if lo_lmt <= metric_today[i] <= hg_lmt:
            data_dic["is_change_err"][i] = 0
            data_dic["log"]["nsigma"][i] = -2
        else:
            # Import helper function for consistency
            from model.model_static.outlier import get_nsigma_cal_res
            val = metric_today[i]
            out_val = get_nsigma_cal_res(
                metric_today[i], lo_lmt, hg_lmt,
                data_dic["param"]["param_list"]["param_sigma"]
            )
            data_dic["log"]["nsigma"][i] = out_val
            
            # Calculate stats for printing (maintain exact behavior)
            avg = (lo_lmt + hg_lmt) / 2
            std = (-lo_lmt + hg_lmt) / 2 / param_sigma
            print(f"nsigma告警-当前值{val:.3f}-上下限[{lo_lmt:.3f},{hg_lmt:.3f}]-avg:{avg:.3f}-std:{std:.3f}-偏离度{out_val:.3f}")
    
    # Set interval data
    data_dic["interval"]["nsigma"] = {
        "highlimit": highlimit.tolist(),
        "lowlimit": lowlimit.tolist()
    }
    
    return data_dic
