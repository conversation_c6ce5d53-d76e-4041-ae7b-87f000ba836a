{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e603e97b", "metadata": {}, "outputs": [], "source": ["def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst"]}, {"cell_type": "code", "execution_count": 2, "id": "b80f43e9", "metadata": {}, "outputs": [], "source": ["import os\n", "os.environ['TRACE_DEFAULT_LLM_BACKEND'] = 'CustomLLM'\n", "os.environ['TRACE_CUSTOMLLM_URL'] = \"http://21.91.109.192:30000/v1\"\n", "os.environ['TRACE_CUSTOMLLM_MODEL'] = \"deepseek-ai/DeepSeek-V3\"\n", "os.environ['TRACE_CUSTOMLLM_API_KEY'] = \"sk-dum7ge6k3FR8ThN7xZmnaWxjv1PPv9OsO7JMjbOu5tVqxDEt\""]}, {"cell_type": "code", "execution_count": 3, "id": "efa0e9ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["strange_sort_list([1, 2, 3, 4])"]}, {"cell_type": "code", "execution_count": 4, "id": "bae307e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (eval:0, dtype=<class 'list'>, data=[1, 2, 3, 4])\n"]}], "source": ["from opto.trace import node, bundle\n", "\n", "@bundle(trainable=True)\n", "def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst\n", "\n", "test_input = [1, 2, 3, 4]\n", "test_output = strange_sort_list(test_input)\n", "print(test_output)"]}, {"cell_type": "code", "execution_count": 5, "id": "6a308a91", "metadata": {}, "outputs": [], "source": ["def get_feedback(predict, target):\n", "    if predict == target:\n", "        return \"test case passed!\"\n", "    else:\n", "        return \"test case failed!\""]}, {"cell_type": "code", "execution_count": 6, "id": "6c7caa32", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using CustomLLM as the default LLM backend.\n", "Training Epoch 0\n", "Training Epoch 1\n"]}], "source": ["from opto.optimizers import OptoPrime\n", "from opto import trace\n", "\n", "test_ground_truth = [1, 4, 2, 3]\n", "test_input = [1, 2, 3, 4]\n", "\n", "epoch = 2\n", "\n", "optimizer = OptoPrime(strange_sort_list.parameters())\n", "\n", "for i in range(epoch):\n", "    print(f\"Training Epoch {i}\")\n", "    try:\n", "        test_output = strange_sort_list(test_input)\n", "        feedback = get_feedback(test_output, test_ground_truth)\n", "    except trace.ExecutionError as e:\n", "        feedback = e.exception_node.data\n", "        test_output = e.exception_node\n", "    \n", "    correctness = test_output.eq(test_ground_truth)\n", "    \n", "    if correctness:\n", "        break\n", "\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(correctness, feedback)\n", "    optimizer.step()"]}, {"cell_type": "code", "execution_count": 7, "id": "8420020c", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "    <style>\n", "        :root {\n", "            --text-color: #1c1c1c;\n", "            --bg-color: #ffffff;\n", "            --trace-bg: #e0e0e0;\n", "            --trace-border: #9e9e9e;\n", "            --feedback-bg: #ffb3ba;\n", "            --feedback-border: #ff6b6b;\n", "            --reason-bg: #baffc9;\n", "            --reason-border: #4caf50;\n", "            --improve-bg: #ffffff;\n", "            --improve-border: #4d9de0;\n", "        }\n", "        @media (prefers-color-scheme: dark) {\n", "            :root {\n", "                --text-color: #e0e0e0;\n", "                --bg-color: #121212;\n", "                --trace-bg: #2a2a2a;\n", "                --trace-border: #555555;\n", "                --feedback-bg: #5c2b30;\n", "                --feedback-border: #ff6b6b;\n", "                --reason-bg: #1e3a2b;\n", "                --reason-border: #4caf50;\n", "                --improve-bg: #121212;\n", "                --improve-border: #4d9de0;\n", "            }\n", "        }\n", "    </style>\n", "\n", "    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin-bottom: 10px; color: var(--text-color);\">\n", "    \n", "        <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "            <div style=\"flex-grow: 1; background-color: var(--trace-bg); border: 2px solid var(--trace-border); padding: 10px; border-radius: 5px; width: 550px;\">\n", "                <p><b><PERSON></b></p>\n", "                <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; color: var(--text-color);\">\n", "\n", "#Code\n", "eval1 = eval(lst=lst1, __code=__code0)\n", "eq0 = eq(x=eval1, y=list0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "[eq] This is an eq operator of x and y..\n", "\n", "#Variables\n", "(code) __code0:def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst\n", "\n", "#Constraints\n", "(code) __code0: The code should start with:\n", "def strange_sort_list(lst):\n", "\n", "#Inputs\n", "(list) lst1=[1, 2, 3, 4]\n", "(list) list0=[1, 4, 2, 3]\n", "\n", "#Outputs\n", "(bool) eq0=False\n", "\n", "</pre>\n", "            </div>\n", "            <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--trace-border);\">\n", "                g<sub>0</sub>\n", "            </div>\n", "        </div>\n", "        \n", "    <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "        <div style=\"flex-grow: 1; background-color: var(--feedback-bg); border: 2px solid var(--feedback-border); padding: 10px; border-radius: 5px;\">\n", "            <p style=\"margin: 0;\"><b>Feedback: </b>test case failed!</p>\n", "        </div>\n", "        <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--feedback-border);\">\n", "            f<sub>0</sub>\n", "        </div>\n", "    </div>\n", "\n", "    <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "        <div style=\"flex-grow: 1; background-color: var(--reason-bg); border: 2px solid var(--reason-border); padding: 10px; border-radius: 5px; width: 550px;\">\n", "            <p style=\"margin: 0;\"><b>Reasoning: </b>1. The #Instruction asks to change the value of the variables in #Variables to improve the output based on the given feedback. The feedback indicates that the test case failed, meaning the function `strange_sort_list` did not produce the expected output `[1, 4, 2, 3]` when given the input `[1, 2, 3, 4]`. \n", "\n", "2. Looking at the #Code, the function `strange_sort_list` is expected to return a list sorted in a 'strange' order, starting with the minimum value, then the maximum of the remaining integers, then the minimum, and so on. The current implementation simply sorts the list in ascending order, which does not meet the requirement, as evidenced by `eval1 = [1, 2, 3, 4]` (which is just the sorted list) and `eq0=False` (which shows the output did not match `list0 = [1, 4, 2, 3]`). \n", "\n", "3. To fix this, we need to modify `__code0` to implement the 'strange sort' correctly. The function should alternate between the minimum and maximum of the remaining elements.</p>\n", "        </div>\n", "        <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--reason-border);\">\n", "            r<sub>1</sub>\n", "        </div>\n", "    </div>\n", "    \n", "        <div style=\"display: flex; align-items: stretch; margin-bottom: 20px;\">\n", "            <div style=\"flex-grow: 1; background-color: var(--improve-bg); border: 2px solid var(--improve-border); padding: 10px; border-radius: 5px;\">\n", "                <p><b>Improvement</b></p>\n", "                <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; font-family: monospace; background-color: var(--improve-bg); color: var(--text-color);\">__code0:\n", "\n", "def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    result = []\n", "    lst_sorted = sorted(lst)\n", "    while lst_sorted:\n", "        result.append(lst_sorted.pop(0))\n", "        if lst_sorted:\n", "            result.append(lst_sorted.pop())\n", "    return result\n", "\n", "</pre>\n", "            </div>\n", "            <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--improve-border);\">\n", "                a<sub>1</sub>\n", "            </div>\n", "        </div>\n", "        </div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def render_opt_step(step_idx, optimizer, no_trace_graph=False, no_improvement=False):\n", "    import json\n", "    import re\n", "    from IPython.display import display, HTML\n", "\n", "    idx = step_idx\n", "    llm_response = json.loads(optimizer.log[idx][\"response\"])\n", "    r1 = llm_response[\"reasoning\"]\n", "\n", "    if llm_response.get(\"suggestion\"):\n", "        a1 = \"\".join(\n", "            [\n", "                f\"{var_name}:\\n\\n{var_body}\\n\\n\"\n", "                for var_name, var_body in llm_response[\"suggestion\"].items()\n", "            ]\n", "        )\n", "    elif llm_response.get(\"answer\") is not None:\n", "        a1 = llm_response[\"answer\"]\n", "    else:\n", "        a1 = \"<ERROR> NULL/INVALID RESPONSE\"\n", "\n", "    pi = optimizer.summary_log[idx][\"problem_instance\"]  # full\n", "    f1 = pi.feedback\n", "\n", "    masked = [\"#Feedback\", \"#Others\", \"#Instruction\"]\n", "    pi = optimizer.problem_instance(optimizer.summary_log[idx][\"summary\"], mask=masked)\n", "\n", "    # a hack to remove \"#Feedback:\" because it has a colon\n", "    pi = str(pi)\n", "    pi = pi.replace(\"#Feedback:\", \"#Feedback\")\n", "\n", "    for m in masked:\n", "        pi = pi.replace(m + \"\\n\", \"\")\n", "\n", "    # a quick processing to reduce multiple empty lines to one\n", "    pi = re.sub(r\"\\n\\s*\\n\", \"\\n\\n\", pi)\n", "    g1 = pi\n", "\n", "    html_template = f\"\"\"\n", "    <style>\n", "        :root {{\n", "            --text-color: #1c1c1c;\n", "            --bg-color: #ffffff;\n", "            --trace-bg: #e0e0e0;\n", "            --trace-border: #9e9e9e;\n", "            --feedback-bg: #ffb3ba;\n", "            --feedback-border: #ff6b6b;\n", "            --reason-bg: #baffc9;\n", "            --reason-border: #4caf50;\n", "            --improve-bg: #ffffff;\n", "            --improve-border: #4d9de0;\n", "        }}\n", "        @media (prefers-color-scheme: dark) {{\n", "            :root {{\n", "                --text-color: #e0e0e0;\n", "                --bg-color: #121212;\n", "                --trace-bg: #2a2a2a;\n", "                --trace-border: #555555;\n", "                --feedback-bg: #5c2b30;\n", "                --feedback-border: #ff6b6b;\n", "                --reason-bg: #1e3a2b;\n", "                --reason-border: #4caf50;\n", "                --improve-bg: #121212;\n", "                --improve-border: #4d9de0;\n", "            }}\n", "        }}\n", "    </style>\n", "\n", "    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin-bottom: 10px; color: var(--text-color);\">\n", "    \"\"\"\n", "\n", "    if not no_trace_graph:\n", "        html_template += f\"\"\"\n", "        <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "            <div style=\"flex-grow: 1; background-color: var(--trace-bg); border: 2px solid var(--trace-border); padding: 10px; border-radius: 5px; width: 550px;\">\n", "                <p><b><PERSON></b></p>\n", "                <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; color: var(--text-color);\">{g1}</pre>\n", "            </div>\n", "            <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--trace-border);\">\n", "                g<sub>{idx}</sub>\n", "            </div>\n", "        </div>\n", "        \"\"\"\n", "\n", "    html_template += f\"\"\"\n", "    <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "        <div style=\"flex-grow: 1; background-color: var(--feedback-bg); border: 2px solid var(--feedback-border); padding: 10px; border-radius: 5px;\">\n", "            <p style=\"margin: 0;\"><b>Feedback: </b>{f1}</p>\n", "        </div>\n", "        <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--feedback-border);\">\n", "            f<sub>{idx}</sub>\n", "        </div>\n", "    </div>\n", "\n", "    <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "        <div style=\"flex-grow: 1; background-color: var(--reason-bg); border: 2px solid var(--reason-border); padding: 10px; border-radius: 5px; width: 550px;\">\n", "            <p style=\"margin: 0;\"><b>Reasoning: </b>{r1}</p>\n", "        </div>\n", "        <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--reason-border);\">\n", "            r<sub>{idx + 1}</sub>\n", "        </div>\n", "    </div>\n", "    \"\"\"\n", "\n", "    if not no_improvement:\n", "        html_template += f\"\"\"\n", "        <div style=\"display: flex; align-items: stretch; margin-bottom: 20px;\">\n", "            <div style=\"flex-grow: 1; background-color: var(--improve-bg); border: 2px solid var(--improve-border); padding: 10px; border-radius: 5px;\">\n", "                <p><b>Improvement</b></p>\n", "                <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; font-family: monospace; background-color: var(--improve-bg); color: var(--text-color);\">{a1}</pre>\n", "            </div>\n", "            <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: var(--improve-border);\">\n", "                a<sub>{idx + 1}</sub>\n", "            </div>\n", "        </div>\n", "        \"\"\"\n", "\n", "    html_template += \"</div>\"\n", "\n", "    display(HTML(html_template))\n", "\n", "render_opt_step(0, optimizer)"]}, {"cell_type": "code", "execution_count": 3, "id": "57aca232", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 2, 3, 4])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "a = np.array([1, 2, 3, 4])\n", "b = np.array([5, 6, 7, 8])\n", "np.min([a, b],axis =0)"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis (3.12.9)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}